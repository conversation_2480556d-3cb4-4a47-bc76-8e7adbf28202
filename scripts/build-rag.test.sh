#!/bin/bash
set -e
echo "start RAG test build"
project="qamate"
output='dist'
echo "node $(node -v)"
echo "yarn $(yarn -v)"
yarn
yarn add @baidu/ep-to-bos
npm run build:test
find ${output}/ -type f -path "*"|xargs  sed -i".bak" "s/fe-cdn.cdn.bcebos.com\/project\/${project}/fe-cdn.cdn.bcebos.com\/project\/${project}\/$1/g"
node node_modules/@baidu/ep-to-bos/index.js project/${project}/$1 ${output}/
echo "RAG test build success!!!"
