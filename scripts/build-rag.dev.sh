#!/bin/bash
set -e
echo "start RAG dev build"
project="qe-rag"
output='dist'

# 创建输出目录
mkdir -p "output"

echo "node $(node -v)"
echo "yarn $(yarn -v)"

echo "=== 开始构建主应用 ==="
yarn
yarn add @baidu/ep-to-bos
npm run build:rag

# 检查是否存在 React 微应用
if [ -d "react-doc-table" ]; then
    echo "=== 开始构建React微应用 ==="
    # 进入React微应用目录
    cd react-doc-table
    # 安装微应用依赖
    npm i
    # 构建微应用 - 开发模式
    npm run build
    cd ..

    # 复制React微应用构建产物
    echo "=== 复制React微应用构建产物到 dist/micro-apps/react-doc-table ==="
    mkdir -p ./dist/micro-apps/react-doc-table
    cp -r ./react-doc-table/dist/* ./dist/micro-apps/react-doc-table/
fi

echo "=== 处理CDN路径替换 ==="
find ${output}/ -type f -path "*"|xargs  sed -i".bak" "s/fe-cdn.cdn.bcebos.com\/project\/${project}/fe-cdn.cdn.bcebos.com\/project\/${project}\/$1/g"

echo "=== 上传到BOS ==="
node node_modules/@baidu/ep-to-bos/index.js project/${project}/$1 ${output}/

echo "=== 压缩打包构建产物 ==="
zip -r dist.zip dist

echo "=== 复制构建产物到输出目录 ==="
cp ./dist.zip ./output/

echo "=== 构建完成，输出目录内容 ==="
ls -la ./output/

echo "RAG dev build success!!!"
