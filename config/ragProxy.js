import path from 'path';
import fs from 'fs';

// RAG 代理配置

export const createRagProxyConfig = (target = 'https://qerag-test.baidu-int.com') => {
    return {
        target,
        changeOrigin: true,
        secure: false,
        // 流式响应的终极配置
        selfHandleResponse: true, // 自己处理响应
        onProxyReq: (proxyReq, req) => {
            try {
                const cookieFilePath = path.join(process.cwd(), '.cookie');
                if (fs.existsSync(cookieFilePath)) {
                    const cookieContent = fs.readFileSync(cookieFilePath, 'utf8');
                    if (cookieContent.trim()) {
                        proxyReq.setHeader('Cookie', cookieContent);
                    }
                }
            } catch (error) {
                console.log('读取 cookie 文件时出错:', error);
            }
        },
        onProxyRes: (proxyRes, req, res) => {
            if (req.url.includes('/stream')) {

                // 立即设置响应头，禁用所有缓冲
                res.writeHead(proxyRes.statusCode, {
                    'Content-Type': proxyRes.headers['content-type'] || 'text/event-stream',
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache',
                    'Expires': '0',
                    'Connection': 'keep-alive',
                    'Transfer-Encoding': 'chunked',
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
                    'X-Accel-Buffering': 'no'
                });

                // 禁用 Node.js 的内部缓冲
                res.socket?.setNoDelay(true);
                res.socket?.setTimeout(0);

                // 逐块传输，立即刷新
                let chunkCount = 0;
                proxyRes.on('data', (chunk) => {
                    chunkCount++;
                    res.write(chunk);
                    // 强制刷新缓冲区
                    if (res.flush) res.flush();
                });

                proxyRes.on('end', () => {
                    res.end();
                });

                proxyRes.on('error', (err) => {
                    console.error('流式响应错误:', err);
                    res.end();
                });
            } else {
                // 普通响应的处理
                res.writeHead(proxyRes.statusCode, proxyRes.headers);
                proxyRes.pipe(res, { end: true });
            }
        },
        pathRewrite: { '^/rag/api': '/rag/api' }
    };
};

// 根据环境获取 RAG 代理目标地址

export const getRagProxyTarget = () => {
    const ENV_MODE = process.env.ENV_MODE;
    
    switch (ENV_MODE) {
        case 'prod':
            return 'https://qerag.baidu-int.com';
        case 'test':
            return 'https://qerag-test.baidu-int.com';
        default:
            return 'https://qerag-test.baidu-int.com';
    }
};

// 默认导出 RAG 代理配置
export default createRagProxyConfig(getRagProxyTarget());
