import { useState } from 'react';
import { useLocation } from 'umi';
import { Select, message, Avatar } from 'antd';
import { CaretDownOutlined, UserOutlined } from '@ant-design/icons';
import { post } from 'COMMON/utils/requestUtils';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import styles from './MemberSelect.module.less';

const USER_PICKER_OPTIONS = 'user_picker_options_map';

const MemberSelect = (props) => {
    const [, contextHolder] = message.useMessage();
    const location = useLocation();
    const {
        isSetLocalStorage,
        variant,
        mode,
        multiple = true, // 新增：控制单选/多选，默认多选
        username
    } = props;
    const [data, setData] = useState([]);
    const setLocalStorage = () => {
        try {
            // 本地存储
            const optionsMap = JSON.parse(localStorage.getItem(USER_PICKER_OPTIONS) || '{}');
            optionsMap[id] = data;
            localStorage.setItem(USER_PICKER_OPTIONS, JSON.stringify(optionsMap));
        } catch (error) { }
    };
    const fetchSuggestions = async (value, callback) => {
        post('/core/user/apigo/list', {queryName: value, searchName: username})
            .then((data) => {
                callback(data?.userInfoList);
                isSetLocalStorage && setLocalStorage();
            });
    };
    // TODO:现在暂未打平 先兼容 后续推进rag服务端打平
    const ragFetch = async (value, callback) => {
        post('rag/api/user/query', { name: value }).then((data) => {
            callback(data);
            isSetLocalStorage && setLocalStorage();
        });
    };
    const handleSearch = (newValue) => {
        if (newValue) {
            if (location.pathname.includes('/qe_rag')) {
                ragFetch(newValue, setData);
                return;
            }
            fetchSuggestions(newValue, setData);
        } else {
            setData([]);
        }
    };
    return (
        <div className={styles.memberSelect}>
            {contextHolder}
            <Select
                mode={mode ?? (multiple ? 'multiple' : undefined)}
                variant={variant || 'borderless'}
                onSearch={(e) => {
                    handleSearch(e);
                }}
                popupMatchSelectWidth={false}
                suffixIcon={<CaretDownOutlined style={{ pointerEvents: 'none' }} />}
                className={styles.select}
                options={data.map((d) => ({
                    value: d.username || d.name,
                    label: d.username || d.name,
                    avatar: d.avatar,
                    name: d.name,
                    department: d.department || d.departmentName,
                    imageUrl: d.imageUrl
                }))}
                optionRender={(option) => (
                    <div
                        style={{
                            display: 'flex',
                            alignItems: 'center',
                            width: '100%',
                            overflow: 'scroll'
                        }}
                    >
                        <Avatar
                            icon={<UserOutlined />}
                            src={option?.data?.imageUrl}
                            style={{ marginRight: 8 }}
                        />
                        <div>
                            <div>
                                {option?.data?.name
                                    ? `${option?.data?.name} (${option?.data?.label})`
                                    : option?.data?.label}
                            </div>
                            <div style={{ fontSize: 12, color: '#888' }}>
                                {option?.data?.department || option?.data?.departmentName || '暂无部门信息'}
                            </div>
                        </div>
                    </div>
                )}
                allowClear
                showSearch
                filterOption={false}
                notFoundContent={null}
                // placeholder={placeholder}
                {...props}
            />
        </div>
    );
};

export default connectModel([baseModel], (state) => ({
    username: state.common.base.username,
    spaceList: state.common.base.spaceList,
    currentSpace: state.common.base.currentSpace,
    filterOsType: state.common.base.filterOsType
}))(MemberSelect);
