// 工具函数
export const splitMembers = (memberStr) => {
    if (!memberStr) {
        return [];
    }
    return memberStr.split(';').filter(Boolean);
};

// 检查是否有编辑权限（管理员）
export const canEdit = (record, ragUsername) => {
    if (!ragUsername) {
        return false;
    }
    const admins = splitMembers(record.admin);
    return admins.includes(ragUsername);
};

// 检查当前用户是否在该组中（管理员、经理或成员）
export const isUserInGroup = (record, ragUsername) => {
    if (!ragUsername) {
        return false;
    }
    const admins = splitMembers(record.admin);
    const managers = splitMembers(record.manager);
    const members = splitMembers(record.member);

    return (
        admins.includes(ragUsername) ||
        managers.includes(ragUsername) ||
        members.includes(ragUsername)
    );
};