import { useState, useEffect, useCallback } from 'react';
import { Mo<PERSON>, Tabs, Button, Table, Tag, Form, message, Pagination, Select } from 'antd';
import {
    UserOutlined,
    TeamOutlined,
    AuditOutlined,
    PlusOutlined,
    CopyOutlined
} from '@ant-design/icons';
import {
    getJoinedGroupinfo,
    getGroups,
    checkList,
    applyYes,
    applyNo,
    getBuList
} from 'COMMON/api/qe_rag/workgroup';
import commonModel from 'COMMON/models/commonModel';
import { connectModel } from 'COMMON/middleware';
import ragModel from 'COMMON/models/qe_rag/ragModel';
import baseModel from 'COMMON/models/baseModel';
import { getFormatTime } from 'COMMON/utils/dateUtils';
import ApplyGroupDialog from './components/ApplyGroupDialog';
import CreateGroupDialog from './components/CreateGroupDialog';
import MemberSelect from 'COMMON/components/Select/MemberSelect';
import { splitMembers, canEdit, isUserInGroup } from './utils';
import styles from './MemberManageModal.module.less';

const { TabPane } = Tabs;

// 成员标签
const MemberTags = ({ memberStr }) => (
    <div>
        {splitMembers(memberStr).map((member) => (
            <Tag key={member} size="small">
                {member}
            </Tag>
        ))}
    </div>
);

// Token 复制
const TokenCopyCell = ({ token }) => (
    <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
        <span style={{ fontSize: '12px', color: '#666' }}>
            {token ? `${token.slice(0, 8)}...` : ''}
        </span>
        {token && (
            <Button
                size="small"
                type="text"
                icon={<CopyOutlined />}
                onClick={(e) => {
                    e.stopPropagation();
                    navigator.clipboard
                        .writeText(token)
                        .then(() => {
                            message.success('Token已复制到剪贴板');
                        })
                        .catch(() => {
                            message.error('复制失败');
                        });
                }}
                title="复制Token"
            />
        )}
    </div>
);

// 时间格式化渲染
const TimeCell = ({ time }) => getFormatTime(time, 'MM-dd HH:mm') || '无';

// 分页组件
const TablePagination = ({ pagination, onChange }) => (
    <div style={{ marginTop: 16, textAlign: 'center' }}>
        <Pagination
            current={pagination.current}
            pageSize={pagination.pageSize}
            total={pagination.total}
            showSizeChanger
            showQuickJumper
            showTotal={(total) => `共 ${total} 条`}
            onChange={onChange}
        />
    </div>
);

// 通用表格列配置
const getTableColumns = (options = {}) => {
    const { showActions = true, actionRenderer } = options;

    const baseColumns = [
        { title: 'ID', dataIndex: 'id', key: 'id', width: 50 },
        { title: '工作组名称', dataIndex: 'name', key: 'name' },
        {
            title: '管理员',
            dataIndex: 'admin',
            key: 'admin',
            render: (admin) => <MemberTags memberStr={admin} />
        },
        {
            title: '成员',
            dataIndex: 'member',
            key: 'member',
            render: (member) => <MemberTags memberStr={member} />
        },
        { title: '创建者', dataIndex: 'createUser', key: 'createUser' },
        { title: '事业群', dataIndex: 'business', key: 'business' },
        { title: '经理', dataIndex: 'manager', key: 'manager' },
        {
            title: 'Token',
            dataIndex: 'token',
            key: 'token',
            width: 120,
            render: (token) => <TokenCopyCell token={token} />
        },
        {
            title: '创建时间',
            dataIndex: 'createAt',
            key: 'createAt',
            render: (time) => <TimeCell time={time} />
        },
        {
            title: '更新时间',
            dataIndex: 'updateAt',
            key: 'updateAt',
            render: (time) => <TimeCell time={time} />
        }
    ];

    if (showActions && actionRenderer) {
        baseColumns.push({
            title: '操作',
            key: 'action',
            width: 100,
            render: actionRenderer
        });
    }

    return baseColumns;
};

// 我加入的组表格组件
const MyGroupsTable = ({ ragUsername }) => {
    const [data, setData] = useState([]);
    const [loading, setLoading] = useState(false);
    const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 });
    const [editDialogVisible, setEditDialogVisible] = useState(false);
    const [currentEditData, setCurrentEditData] = useState(null);
    const [isEditMode, setIsEditMode] = useState(false);

    const fetchData = useCallback(
        async (params = {}) => {
            setLoading(true);
            try {
                const res = await getJoinedGroupinfo({
                    page: pagination.current,
                    pageSize: pagination.pageSize,
                    ...params
                });
                const dataArray = res?.items || [];
                setData(dataArray);
                setPagination((prev) => ({ ...prev, total: res?.total || dataArray.length }));
            } finally {
                setLoading(false);
            }
        },
        [pagination.current, pagination.pageSize]
    );

    useEffect(() => {
        fetchData();
    }, [fetchData]);

    // 处理编辑操作
    const handleEdit = (record) => {
        if (!canEdit(record, ragUsername)) {
            message.warning('您没有权限编辑该群组');
            return;
        }

        setIsEditMode(true);
        setCurrentEditData({
            ...record,
            managerArray: splitMembers(record.manager),
            memberArray: splitMembers(record.member),
            adminArray: splitMembers(record.admin)
        });
        setEditDialogVisible(true);
    };

    // 处理编辑成功
    const handleEditSuccess = () => {
        setEditDialogVisible(false);
        setCurrentEditData(null);
        fetchData(); // 重新获取数据
        message.success('编辑成功');
    };

    // 操作列渲染
    const actionRenderer = (_, record) => (
        <Button
            size="small"
            type="primary"
            onClick={() => handleEdit(record)}
            disabled={!canEdit(record, ragUsername)}
        >
            编辑
        </Button>
    );

    const columns = getTableColumns({
        actionRenderer
    });

    return (
        <>
            <Table
                columns={columns}
                dataSource={Array.isArray(data) ? data : []}
                loading={loading}
                pagination={false}
                rowKey="id"
                scroll={{ x: 1200, y: 300 }}
            />
            <TablePagination
                pagination={pagination}
                onChange={(page, pageSize) => {
                    setPagination((prev) => ({ ...prev, current: page, pageSize }));
                }}
            />

            <CreateGroupDialog
                visible={editDialogVisible}
                onCancel={() => setEditDialogVisible(false)}
                formData={currentEditData}
                isEdit={isEditMode}
                onSubmitSuccess={handleEditSuccess}
                ragUsername={ragUsername}
            />
        </>
    );
};

// 全部团队表格组件
const AllGroupsTable = ({ refreshTrigger, setCreateDialogVisible, ragUsername }) => {
    const [data, setData] = useState([]);
    const [loading, setLoading] = useState(false);
    const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 });
    const [filterForm] = Form.useForm();
    const [applyDialogVisible, setApplyDialogVisible] = useState(false);
    const [currentApplyGroup, setCurrentApplyGroup] = useState(null);
    const [editDialogVisible, setEditDialogVisible] = useState(false);
    const [currentEditData, setCurrentEditData] = useState(null);
    const [isEditMode, setIsEditMode] = useState(false);
    const [businessOptions, setBusinessOptions] = useState([]);
    const [businessLoading, setBusinessLoading] = useState(false);
    const [existingGroups, setExistingGroups] = useState([]);

    const fetchData = useCallback(
        async (params = {}) => {
            setLoading(true);
            try {
                const res = await getGroups({
                    page: pagination.current,
                    size: pagination.pageSize,
                    ...params
                });
                const items = res.items || [];
                setData(items);
                setPagination((prev) => ({ ...prev, total: res.total || items.length || 0 }));

                // 提取现有工作组名称用于下拉选择
                const groupNames = [...new Set(items.map((item) => item.name).filter(Boolean))];
                setExistingGroups(groupNames);
            } finally {
                setLoading(false);
            }
        },
        [pagination.current, pagination.pageSize]
    );

    useEffect(() => {
        fetchData();
    }, [fetchData, refreshTrigger]);

    // 加载业务单元选项
    useEffect(() => {
        const loadBusinessOptions = async () => {
            try {
                setBusinessLoading(true);
                const res = await getBuList();
                const validOptions = (res || []).filter(
                    (item) => item && typeof item === 'string' && item.trim() !== ''
                );
                setBusinessOptions(validOptions);
            } finally {
                setBusinessLoading(false);
            }
        };

        loadBusinessOptions();
    }, []);

    const handleFilter = () => {
        const values = filterForm.getFieldsValue();
        fetchData(values);
    };

    const handleApply = (record) => {
        const admins = splitMembers(record.admin);
        if (admins.length === 0) {
            message.error('该工作组没有管理员，无法申请');
            return;
        }

        setCurrentApplyGroup({
            id: record.id,
            name: record.name,
            admins: admins
        });
        setApplyDialogVisible(true);
    };

    const handleApplySuccess = () => {
        message.success('申请已提交，等待审核');
    };

    // 处理编辑操作
    const handleEdit = (record) => {
        if (!canEdit(record, ragUsername)) {
            message.warning('您没有权限编辑该群组');
            return;
        }

        setIsEditMode(true);
        setCurrentEditData({
            ...record,
            managerArray: splitMembers(record.manager),
            memberArray: splitMembers(record.member),
            adminArray: splitMembers(record.admin)
        });
        setEditDialogVisible(true);
    };

    // 处理编辑成功
    const handleEditSuccess = () => {
        setEditDialogVisible(false);
        setCurrentEditData(null);
        fetchData(); // 重新获取数据
        message.success('编辑成功');
    };

    // 操作列渲染
    const actionRenderer = (_, record) => {
        const userInGroup = isUserInGroup(record, ragUsername);
        const hasEditPermission = canEdit(record, ragUsername);

        if (userInGroup) {
            // 用户已在组中，显示编辑按钮（仅管理员可编辑）
            return (
                <Button
                    size="small"
                    type="primary"
                    onClick={() => handleEdit(record)}
                    disabled={!hasEditPermission}
                    style={{
                        backgroundColor: hasEditPermission ? '#52c41a' : undefined,
                        borderColor: hasEditPermission ? '#52c41a' : undefined
                    }}
                >
                    编辑
                </Button>
            );
        } else {
            // 用户不在组中，显示申请按钮
            return (
                <Button
                    size="small"
                    type="primary"
                    onClick={() => handleApply(record)}
                    style={{
                        backgroundColor: '#1890ff',
                        borderColor: '#1890ff'
                    }}
                >
                    申请
                </Button>
            );
        }
    };

    const columns = getTableColumns({
        actionRenderer
    });

    return (
        <>
            <Form form={filterForm} layout="inline" style={{ margin: '16px 0' }}>
                <Form.Item name="name" label="工作组名称">
                    <Select
                        placeholder="选择工作组名称"
                        showSearch
                        allowClear
                        style={{ width: 200 }}
                        filterOption={(input, option) =>
                            option?.children?.toLowerCase().indexOf(input.toLowerCase()) >= 0
                        }
                    >
                        {existingGroups.map((groupName) => (
                            <Select.Option key={groupName} value={groupName}>
                                {groupName}
                            </Select.Option>
                        ))}
                    </Select>
                </Form.Item>
                <Form.Item name="manager" label="经理">
                    <MemberSelect
                        placeholder="选择经理"
                        multiple={false}
                        style={{ width: 200 }}
                        variant="outlined"
                    />
                </Form.Item>
                <Form.Item name="business" label="事业群名称">
                    <Select
                        placeholder="选择事业群名称"
                        loading={businessLoading}
                        showSearch
                        allowClear
                        style={{ width: 200 }}
                        filterOption={(input, option) =>
                            option?.children?.toLowerCase().indexOf(input.toLowerCase()) >= 0
                        }
                    >
                        {businessOptions.map((item) => (
                            <Select.Option key={item} value={item}>
                                {item}
                            </Select.Option>
                        ))}
                    </Select>
                </Form.Item>
                <Form.Item>
                    <Button type="primary" onClick={handleFilter}>
                        查询
                    </Button>
                </Form.Item>
                <Form.Item>
                    <Button
                        type="primary"
                        icon={<PlusOutlined />}
                        onClick={() => setCreateDialogVisible(true)}
                        style={{ marginRight: 24 }}
                    >
                        新增群组
                    </Button>
                </Form.Item>
            </Form>
            <Table
                columns={columns}
                dataSource={Array.isArray(data) ? data : []}
                loading={loading}
                pagination={false}
                rowKey="id"
                scroll={{ x: 1200, y: 300 }}
            />
            <TablePagination
                pagination={pagination}
                onChange={(page, pageSize) => {
                    setPagination((prev) => ({ ...prev, current: page, pageSize }));
                }}
            />

            <ApplyGroupDialog
                visible={applyDialogVisible}
                onCancel={() => setApplyDialogVisible(false)}
                groupId={currentApplyGroup?.id}
                groupName={currentApplyGroup?.name}
                admins={currentApplyGroup?.admins || []}
                onSubmitSuccess={handleApplySuccess}
            />

            <CreateGroupDialog
                visible={editDialogVisible}
                onCancel={() => setEditDialogVisible(false)}
                formData={currentEditData}
                isEdit={isEditMode}
                onSubmitSuccess={handleEditSuccess}
                ragUsername={ragUsername}
            />
        </>
    );
};

// 待审批列表组件
const PendingApprovalTable = () => {
    const [data, setData] = useState([]);
    const [loading, setLoading] = useState(false);

    const fetchData = useCallback(async () => {
        setLoading(true);
        try {
            const res = await checkList({});
            setData(res ?? []);
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchData();
    }, [fetchData]);

    const handleApprove = async (record) => {
        await applyYes({
            id: record.id,
            workGroupId: record.workGroupId,
            role: record.role,
            userName: record.username
        });
        message.success('审批成功');
        fetchData();
    };

    const handleReject = async (record) => {
        await applyNo({
            id: record.id,
            workGroupId: record.workGroupId,
            role: record.role,
            userName: record.username
        });
        message.success('拒绝成功');
        fetchData();
    };

    const columns = [
        { title: 'ID', dataIndex: 'workGroupId', key: 'workGroupId' },
        { title: '工作组名称', dataIndex: 'workGroupName', key: 'workGroupName' },
        {
            title: '申请角色',
            dataIndex: 'role',
            key: 'role',
            render: (role) => (
                <Tag color={role === 1 ? 'blue' : 'green'}>{role === 1 ? '管理员' : '成员'}</Tag>
            )
        },
        { title: '申请原因', dataIndex: 'reason', key: 'reason' },
        { title: '申请人', dataIndex: 'userName', key: 'userName' },
        {
            title: '操作',
            key: 'action',
            render: (_, record) => (
                <div>
                    <Button
                        size="small"
                        type="primary"
                        onClick={() => handleApprove(record)}
                        style={{ marginRight: 8 }}
                    >
                        同意
                    </Button>
                    <Button size="small" danger onClick={() => handleReject(record)}>
                        拒绝
                    </Button>
                </div>
            )
        }
    ];

    return (
        <Table
            scroll={{ x: 1200, y: 300 }}
            columns={columns}
            dataSource={Array.isArray(data) ? data : []}
            loading={loading}
            pagination={false}
            rowKey="id"
        />
    );
};

const MemberManageModal = ({ visible, onCancel, ragUsername }) => {
    const [activeTab, setActiveTab] = useState('1');
    const [createDialogVisible, setCreateDialogVisible] = useState(false);
    const [refreshTrigger, setRefreshTrigger] = useState(0);

    const handleCreateSuccess = () => {
        setRefreshTrigger((prev) => prev + 1);
    };

    return (
        <>
            <Modal
                // title={modalTitle}
                open={visible}
                onCancel={onCancel}
                footer={null}
                width={1400}
                height={600}
                className={styles.memberManageModal}
            >
                <Tabs activeKey={activeTab} onChange={setActiveTab} className={styles.groupTabs}>
                    <TabPane
                        tab={
                            <div className={styles.tabLabel}>
                                <UserOutlined className={styles.tabIcon} />
                                <span className={styles.tabText}>我加入的</span>
                            </div>
                        }
                        key="1"
                    >
                        {activeTab === '1' && <MyGroupsTable ragUsername={ragUsername} />}
                    </TabPane>
                    <TabPane
                        tab={
                            <div className={styles.tabLabel}>
                                <TeamOutlined className={styles.tabIcon} />
                                <span className={styles.tabText}>全部团队</span>
                            </div>
                        }
                        key="2"
                    >
                        {activeTab === '2' && (
                            <AllGroupsTable
                                refreshTrigger={refreshTrigger}
                                setCreateDialogVisible={setCreateDialogVisible}
                                ragUsername={ragUsername}
                            />
                        )}
                    </TabPane>
                    <TabPane
                        tab={
                            <div className={styles.tabLabel}>
                                <AuditOutlined className={styles.tabIcon} />
                                <span className={styles.tabText}>待审批列表</span>
                            </div>
                        }
                        key="3"
                    >
                        {activeTab === '3' && <PendingApprovalTable />}
                    </TabPane>
                </Tabs>
            </Modal>

            <CreateGroupDialog
                visible={createDialogVisible}
                onCancel={() => setCreateDialogVisible(false)}
                onSubmitSuccess={handleCreateSuccess}
            />
        </>
    );
};

export default connectModel([baseModel, commonModel, ragModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    workGroupList: state.common.rag.workGroupList,
    ragUsername: state.common.base.ragUsername,
    joinedWorkGroupList: state.common.rag.joinedWorkGroupList,
    allWorkGroupList: state.common.rag.allWorkGroupList,
    embeddingModelList: state.common.rag.embeddingModelList
}))(MemberManageModal);
